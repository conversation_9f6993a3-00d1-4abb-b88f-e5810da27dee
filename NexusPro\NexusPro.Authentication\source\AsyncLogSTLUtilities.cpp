/*
 * NexusPro Authentication Module
 * AsyncLog STL Utilities Implementation
 * 
 * Original Functions: Multiple STL utility functions for AsyncLogInfo
 * Original Addresses: 0x1403C7DB0 - 0x1403C8C60
 * 
 * Purpose: STL utility functions for AsyncLogInfo construction, destruction, and manipulation
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 * 
 * Changes Made:
 * - Consolidated multiple STL utility functions into single file
 * - Fixed malformed template syntax
 * - Added proper includes and namespace
 * - Maintained original decompiled logic
 */

#include "../headers/AsyncLogSTLAllocators.h"
#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace STL {

/**
 * @brief Constructs pair object in-place
 * @param _Ptr Pointer to location to construct at
 * @param _Val Value to copy construct from
 * 
 * Original Function: ??$_Construct@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@U12@@std@@YAXPEAU?$pair@$$CBHPEAVCAsyncLogInfo@@@0@AEBU10@@Z
 * Original Address: 0x1403C7DB0
 * 
 * Constructs a pair object at the specified location using copy construction.
 */
void __fastcall std::_Construct<std::pair<int const,CAsyncLogInfo *>,std::pair<int const,CAsyncLogInfo *>>(
    std::pair<int const,CAsyncLogInfo *> *_Ptr, 
    std::pair<int const,CAsyncLogInfo *> *_Val)
{
    __int64 *v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-68h]@1
    void *_Where; // [sp+20h] [bp-48h]@4
    char *v6; // [sp+28h] [bp-40h]@4
    char v7; // [sp+30h] [bp-38h]@5
    std::pair<int const,CAsyncLogInfo *> *v8; // [sp+70h] [bp+8h]@1
    std::pair<int const,CAsyncLogInfo *> *v9; // [sp+78h] [bp+10h]@1

    v9 = _Val;
    v8 = _Ptr;
    v2 = &v4;
    
    // Initialize debug pattern in local variables
    for (i = 22i64; i; --i) {
        *(_DWORD *)v2 = -858993460;
        v2 = (__int64 *)((char *)v2 + 4);
    }
    
    _Where = v8;
    
    // Use placement new to construct the pair
    v6 = (char *)operator new(0x10ui64, v8);
    if (v6) {
        // Copy the pair data
        qmemcpy(&v7, v9, 0x10ui64);
        qmemcpy(v6, &v7, 0x10ui64);
    }
}

/**
 * @brief Destroys list node object
 * @param _Ptr Pointer to node to destroy
 * 
 * Original Function: ??$_Destroy@U_Node@?$_List_nod@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@std@@YAXPEAU_Node@?$_List_nod@U?$pair@$$CBHPEAVCAsyncLogInfo@@@0@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@0@@@2@@0@@Z
 * Original Address: 0x1403C7F40
 * 
 * Destroys a list node containing an AsyncLogInfo pair.
 */
void __fastcall std::_Destroy<std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node>(
    std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node *_Ptr)
{
    __int64 *v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v3; // [sp+0h] [bp-38h]@1
    std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node *v4; // [sp+40h] [bp+8h]@1

    v4 = _Ptr;
    v1 = &v3;
    
    // Initialize debug pattern in local variables
    for (i = 12i64; i; --i) {
        *(_DWORD *)v1 = -858993460;
        v1 = (__int64 *)((char *)v1 + 4);
    }
    
    // Destroy the pair value in the node
    if (v4) {
        std::pair<int const,CAsyncLogInfo *>::~pair(&v4->_Myval);
    }
}

/**
 * @brief Destroys range of iterators
 * @param _First Iterator to start of range
 * @param _Last Iterator to end of range
 * @param _Cat Iterator category tag
 * 
 * Original Function: ??$_Destroy@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@Uforward_iterator_tag@2@@std@@YAXV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@0@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@0@@@2@@0@0Uforward_iterator_tag@0@@Z
 * Original Address: 0x1403C8C60
 * 
 * Destroys a range of elements specified by iterators.
 */
void __fastcall std::_Destroy<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>,std::forward_iterator_tag>(
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> _First,
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> _Last,
    std::forward_iterator_tag _Cat)
{
    __int64 *v3; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-48h]@1
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> v6; // [sp+20h] [bp-28h]@4
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> v7; // [sp+28h] [bp-20h]@4

    v3 = &v5;
    
    // Initialize debug pattern in local variables
    for (i = 16i64; i; --i) {
        *(_DWORD *)v3 = -858993460;
        v3 = (__int64 *)((char *)v3 + 4);
    }
    
    v6 = _First;
    v7 = _Last;
    
    // Iterate through range and destroy each element
    while (v6._Ptr != v7._Ptr) {
        // Destroy the current element
        std::pair<int const,CAsyncLogInfo *>::~pair(&v6._Ptr->_Myval);
        
        // Move to next element
        v6._Ptr = v6._Ptr->_Next;
    }
}

/**
 * @brief Copy constructs pair object
 * @param _Dest Destination pair
 * @param _Src Source pair to copy from
 * 
 * Helper function for copying AsyncLogInfo pairs.
 */
void __fastcall CopyConstructAsyncLogPair(
    std::pair<int const,CAsyncLogInfo *> *_Dest,
    const std::pair<int const,CAsyncLogInfo *> *_Src)
{
    if (_Dest && _Src) {
        _Dest->first = _Src->first;
        _Dest->second = _Src->second;
    }
}

/**
 * @brief Destroys pair object
 * @param _Ptr Pointer to pair to destroy
 * 
 * Helper function for destroying AsyncLogInfo pairs.
 */
void __fastcall DestroyAsyncLogPair(
    std::pair<int const,CAsyncLogInfo *> *_Ptr)
{
    if (_Ptr) {
        // For simple pairs with pointers, no special destruction needed
        // Just clear the values
        _Ptr->first = 0;
        _Ptr->second = nullptr;
    }
}

} // namespace STL
} // namespace Authentication
} // namespace NexusPro
